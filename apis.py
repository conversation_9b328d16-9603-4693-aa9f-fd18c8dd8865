from itertools import product

from jwt_generator import jwt_token
import cache
import requests


def __get_google_apis_token():
    cached_token = cache.load_cached_token()
    if cached_token:
        return cached_token

    token_url = "https://oauth2.googleapis.com/token"
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    body = {
        "grant_type": "urn:ietf:params:oauth:grant-type:jwt-bearer",
        "assertion": jwt_token,
    }
    response = requests.post(token_url, headers=headers, data=body)

    #token
    access_token = response.json()["access_token"]
    cache.save_token_to_cache(access_token,expires_in=60*60) #一小时过期

    return access_token

base_url = "https://androidpublisher.googleapis.com/androidpublisher/v3/applications"
package_name = None
product_id = None
purchase_token = None
purchase_order_id = None
token = __get_google_apis_token()

def get_order(body):
    global package_name, product_id, purchase_token, purchase_order_id  # 声明要用外部变量

    package_name = body["package_name"]
    purchase_order_id = body["order_id"]

    url = f"{base_url}/{package_name}/orders/{purchase_order_id}"
    response = requests.get(url, headers=generate_headers())
    json_response = response.json()

    product_id = json_response["lineItems"][0]["productId"]
    purchase_token = json_response["purchaseToken"]



def product_consume():

    # 请求 URL
    url = f"{base_url}/{package_name}/purchases/products/{product_id}/tokens/{purchase_token}:consume"

    # 发起 POST 请求
    response = requests.post(url, headers=generate_headers())

    # 解析结果
    if response.status_code == 204:
        print("消费成功")
    else:
        print("该订单消费失败，可能是由于已经被消费了")


def product_details():
    #请求url
    url = f"{base_url}/{package_name}/purchases/products/{product_id}/tokens/{purchase_token}"
    response = requests.get(url, headers=generate_headers())

    if response.status_code == 200:
        print(f"内购详情: \n{response.text}")
    else:
        print(f"获取内购详情失败: code-{response.status_code} - {response.text}")


def generate_headers():
    return {
        "Authorization": f"Bearer {token}",
        "Accept": "application/json"
    }