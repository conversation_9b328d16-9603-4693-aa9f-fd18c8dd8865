#!/usr/bin/env python3
"""
使用示例 - 展示如何使用解耦的JWT生成器和文件选择器
"""

from jwt_generator import JWTGenerator, generate_jwt_from_file
from file_selector import TkinterFileSelector, WebFileSelector, validate_google_api_file

def example_1_simple_usage():
    """示例1: 简单使用 - 直接从文件生成JWT"""
    print("=== 示例1: 简单使用 ===")
    
    # 如果你已经有文件路径，可以直接使用
    file_path = "googleapi.json"  # 替换为你的文件路径
    
    try:
        result = generate_jwt_from_file(file_path)
        print(f"JWT Token: {result['token']}")
        print(f"过期时间: {result['expiry_time']}")
        print(f"客户端邮箱: {result['client_email']}")
    except Exception as e:
        print(f"错误: {e}")

def example_2_class_usage():
    """示例2: 使用类接口 - 更灵活的控制"""
    print("\n=== 示例2: 类接口使用 ===")
    
    generator = JWTGenerator()
    
    # 检查是否有上次使用的文件
    last_file = generator.get_last_file_path()
    if last_file:
        print(f"上次使用的文件: {last_file}")
    
    try:
        # 加载配置文件
        file_path = "googleapi.json"  # 替换为你的文件路径
        generator.load_google_api_config(file_path)
        
        # 生成JWT Token，自定义参数
        result = generator.generate_jwt_token(
            scope="https://www.googleapis.com/auth/androidpublisher",
            hours=2  # 2小时过期
        )
        
        print(f"JWT Token: {result['token']}")
        print(f"过期时间: {result['expiry_time']}")
        print(f"作用域: {result['scope']}")
        
        # 获取token信息
        token_info = generator.get_token_info()
        print(f"当前token信息: {token_info}")
        
    except Exception as e:
        print(f"错误: {e}")

def example_3_tkinter_file_selector():
    """示例3: 使用tkinter文件选择器"""
    print("\n=== 示例3: Tkinter文件选择器 ===")
    
    selector = TkinterFileSelector()
    
    # 选择文件（会记住上次的选择）
    file_path = selector.select_file_with_last_option()
    
    if file_path:
        # 验证文件
        is_valid, message = selector.validate_file(file_path)
        
        if is_valid:
            print(f"选择的文件有效: {file_path}")
            
            # 生成JWT
            try:
                result = generate_jwt_from_file(file_path)
                selector.show_message("成功", f"JWT Token生成成功!\n过期时间: {result['expiry_time']}")
            except Exception as e:
                selector.show_message("错误", f"生成JWT失败: {str(e)}", "error")
        else:
            print(f"文件无效: {message}")
            selector.show_message("错误", f"文件无效: {message}", "error")
    else:
        print("未选择文件")

def example_4_web_file_selector():
    """示例4: Web文件选择器（模拟）"""
    print("\n=== 示例4: Web文件选择器 ===")
    
    selector = WebFileSelector()
    
    # 模拟文件上传内容
    try:
        with open("googleapi.json", "r", encoding="utf-8") as f:
            file_content = f.read()
        
        # 处理上传的文件
        success, result = selector.handle_file_upload(file_content, "googleapi.json")
        
        if success:
            print(f"文件上传成功: {result}")
            
            # 获取文件信息
            file_info = selector.get_file_info()
            print(f"文件信息: {file_info}")
            
        else:
            print(f"文件上传失败: {result}")
            
    except FileNotFoundError:
        print("找不到 googleapi.json 文件，跳过此示例")
    except Exception as e:
        print(f"错误: {e}")

def example_5_validation():
    """示例5: 文件验证"""
    print("\n=== 示例5: 文件验证 ===")
    
    test_files = ["googleapi.json", "nonexistent.json", "invalid.json"]
    
    for file_path in test_files:
        is_valid, message = validate_google_api_file(file_path)
        print(f"{file_path}: {'✅ 有效' if is_valid else '❌ 无效'} - {message}")

if __name__ == "__main__":
    print("JWT生成器和文件选择器使用示例")
    print("=" * 50)
    
    # 运行所有示例
    example_1_simple_usage()
    example_2_class_usage()
    
    # 注意：以下示例需要GUI环境
    # example_3_tkinter_file_selector()
    
    example_4_web_file_selector()
    example_5_validation()
    
    print("\n" + "=" * 50)
    print("示例完成！")
    print("\n使用说明:")
    print("1. 简单使用: from jwt_generator import generate_jwt_from_file")
    print("2. 高级使用: from jwt_generator import JWTGenerator")
    print("3. Tkinter GUI: from file_selector import TkinterFileSelector")
    print("4. Web界面: from file_selector import WebFileSelector")
    print("5. 运行 python gui.py 启动Tkinter界面")
    print("6. 运行 python web_gui.py 启动Web界面")
