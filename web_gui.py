#!/usr/bin/env python3
import http.server
import socketserver
import json
import threading
import webbrowser
import os
import sys
from datetime import datetime
from apis import get_order, product_consume, product_details, set_jwt_generator
from file_selector import WebFileSelector
from jwt_generator import JWTGenerator

# 全局变量存储日志
captured_logs = []
log_lock = threading.Lock()

# 全局文件选择器
file_selector = WebFileSelector()
jwt_generator = JWTGenerator()

# 设置API使用的JWT生成器
set_jwt_generator(jwt_generator)

def add_log(message):
    """添加日志消息"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    with log_lock:
        captured_logs.append(f"[{timestamp}] {message}")

class LogCapture:
    """捕获标准输出和错误输出"""
    def write(self, text):
        if text.strip():  # 忽略空行
            add_log(text.strip())
    
    def flush(self):
        pass

# 创建日志捕获器
log_capture = LogCapture()

class APIHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            html_content = '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Play Developer API Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="file"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        button { background-color: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background-color: #45a049; }
        button:disabled { background-color: #cccccc; cursor: not-allowed; }
        .run-all { background-color: #2196F3; font-size: 16px; font-weight: bold; }
        .run-all:hover { background-color: #1976D2; }
        .output { background-color: #f8f8f8; border: 1px solid #ddd; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; white-space: pre-wrap; }
        .button-group { text-align: center; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google Play Developer API Tool</h1>
        
        <div class="form-group">
            <label for="package_name">Package Name:</label>
            <input type="text" id="package_name" placeholder="e.g., com.tf.localuv">
        </div>
        
        <div class="form-group">
            <label for="order_id">Order ID:</label>
            <input type="text" id="order_id" placeholder="e.g., GPA.3314-3666-3496-87495">
        </div>
        
        <div class="form-group">
            <label for="api_file">Google API JSON File:</label>
            <input type="file" id="api_file" accept=".json" onchange="handleFileSelection()">
            <div id="file_status" style="margin-top: 5px; font-size: 12px; padding: 5px; border-radius: 3px;"></div>
            <div id="file_details" style="margin-top: 5px; font-size: 11px; color: #666;"></div>
        </div>
        
        <div class="button-group">
            <button id="jwt_btn" onclick="generateJWT()" disabled>Generate JWT Token</button>
        </div>

        <div class="button-group">
            <button id="order_btn" onclick="getOrderInfo()" disabled>1. Get Order Info</button>
            <button id="consume_btn" onclick="consumeProduct()" disabled>2. Consume Product</button>
            <button id="details_btn" onclick="productDetails()" disabled>3. Product Details</button>
        </div>

        <div class="button-group">
            <button class="run-all" onclick="runAllSteps()">Run All Steps</button>
        </div>
        
        <div class="form-group">
            <label for="output">Output:</label>
            <div id="output" class="output"></div>
        </div>
        
        <div class="button-group">
            <button onclick="clearOutput()">Clear Output</button>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedFile = null;
        let fileContent = null;
        let isFileValid = false;

        // 基础函数
        function logOutput(message) {
            const output = document.getElementById('output');
            output.textContent += message + '\\n';
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
        
        // 数据保存
        function saveData() {
            const data = {
                package_name: document.getElementById('package_name').value,
                order_id: document.getElementById('order_id').value
            };
            localStorage.setItem('api_tool_data', JSON.stringify(data));
        }
        
        // 网络请求
        async function makeRequest(endpoint, data) {
            try {
                logOutput('⏳ 正在处理请求...');
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data || {})
                });
                const result = await response.json();
                
                // 显示服务器返回的日志
                if (result.logs && result.logs.length > 0) {
                    result.logs.forEach(log => logOutput(log));
                }
                
                return result;
            } catch (error) {
                logOutput('❌ 网络错误: ' + error.message);
                return { success: false, error: error.message };
            }
        }
        
        // 文件处理函数
        async function handleFileSelection() {
            const fileInput = document.getElementById('api_file');
            const fileStatus = document.getElementById('file_status');
            const fileDetails = document.getElementById('file_details');
            const jwtBtn = document.getElementById('jwt_btn');
            const orderBtn = document.getElementById('order_btn');

            // 重置状态
            selectedFile = null;
            fileContent = null;
            isFileValid = false;
            updateButtonStates();

            if (fileInput.files.length === 0) {
                fileStatus.textContent = '';
                fileStatus.style.backgroundColor = '';
                fileDetails.textContent = '';
                return;
            }

            const file = fileInput.files[0];
            selectedFile = file;

            fileStatus.textContent = '⏳ 正在验证文件...';
            fileStatus.style.color = 'orange';
            fileStatus.style.backgroundColor = '#fff3cd';

            try {
                // 读取文件内容
                fileContent = await file.text();

                // 验证JSON格式
                let jsonData;
                try {
                    jsonData = JSON.parse(fileContent);
                } catch (e) {
                    throw new Error('文件不是有效的JSON格式');
                }

                // 验证必要字段
                const requiredFields = ['client_email', 'token_uri', 'private_key'];
                const missingFields = requiredFields.filter(field => !jsonData[field]);

                if (missingFields.length > 0) {
                    throw new Error(`缺少必要字段: ${missingFields.join(', ')}`);
                }

                // 验证成功
                isFileValid = true;
                fileStatus.textContent = '✅ 文件验证成功';
                fileStatus.style.color = 'green';
                fileStatus.style.backgroundColor = '#d4edda';

                fileDetails.textContent = `文件: ${file.name} (${(file.size / 1024).toFixed(1)} KB) | 客户端: ${jsonData.client_email}`;

                updateButtonStates();
                logOutput('📁 Google API文件选择成功: ' + file.name);
                logOutput('📧 客户端邮箱: ' + jsonData.client_email);

            } catch (error) {
                isFileValid = false;
                fileStatus.textContent = '❌ ' + error.message;
                fileStatus.style.color = 'red';
                fileStatus.style.backgroundColor = '#f8d7da';
                fileDetails.textContent = '';
                updateButtonStates();
                logOutput('❌ 文件验证失败: ' + error.message);
            }
        }

        function updateButtonStates() {
            const jwtBtn = document.getElementById('jwt_btn');
            const orderBtn = document.getElementById('order_btn');
            const consumeBtn = document.getElementById('consume_btn');
            const detailsBtn = document.getElementById('details_btn');

            // JWT按钮：需要有效文件
            jwtBtn.disabled = !isFileValid;

            // API操作按钮：需要有效文件
            orderBtn.disabled = !isFileValid;

            // 消费和详情按钮：需要先执行获取订单信息
            // 这些按钮的状态会在getOrderInfo成功后更新
        }

        function checkFileBeforeOperation(operationName) {
            if (!selectedFile) {
                alert('请先选择Google API JSON文件');
                logOutput('❌ ' + operationName + '失败: 未选择文件');
                return false;
            }

            if (!isFileValid) {
                alert('选择的文件无效，请选择有效的Google API JSON文件');
                logOutput('❌ ' + operationName + '失败: 文件无效');
                return false;
            }

            return true;
        }

        async function generateJWT() {
            if (!checkFileBeforeOperation('JWT Token生成')) {
                return;
            }

            try {
                const result = await makeRequest('/api/generate_jwt', {
                    file_content: fileContent,
                    filename: selectedFile.name
                });

                if (result.success) {
                    logOutput('✅ JWT Token 生成成功');
                    logOutput('🔑 Token: ' + result.token.substring(0, 50) + '...');
                    logOutput('⏰ 过期时间: ' + result.expiry_time);
                    logOutput('📧 客户端邮箱: ' + result.client_email);
                } else {
                    logOutput('❌ JWT Token 生成失败: ' + result.error);
                }
            } catch (error) {
                logOutput('❌ JWT Token 生成错误: ' + error.message);
            }
        }

        // API调用函数
        async function getOrderInfo() {
            if (!checkFileBeforeOperation('获取订单信息')) {
                return;
            }

            const packageName = document.getElementById('package_name').value.trim();
            const orderId = document.getElementById('order_id').value.trim();

            if (!packageName || !orderId) {
                alert('请输入包名和订单ID');
                return;
            }

            try {
                const result = await makeRequest('/api/get_order', {
                    package_name: packageName,
                    order_id: orderId,
                    file_content: fileContent,
                    filename: selectedFile.name
                });

                if (result.success) {
                    document.getElementById('consume_btn').disabled = false;
                    document.getElementById('details_btn').disabled = false;
                    logOutput('✅ 订单信息获取成功');
                    saveData(); // 保存输入的数据
                } else {
                    logOutput('❌ 获取订单信息失败: ' + result.error);
                }
            } catch (error) {
                logOutput('❌ 获取订单信息错误: ' + error.message);
            }
        }
        
        async function consumeProduct() {
            if (!checkFileBeforeOperation('产品消费')) {
                return;
            }

            try {
                const result = await makeRequest('/api/consume', {
                    file_content: fileContent,
                    filename: selectedFile.name
                });

                if (result.success) {
                    logOutput('✅ 产品消费成功');
                } else {
                    logOutput('❌ 产品消费失败: ' + result.error);
                }
            } catch (error) {
                logOutput('❌ 产品消费错误: ' + error.message);
            }
        }

        async function productDetails() {
            if (!checkFileBeforeOperation('获取产品详情')) {
                return;
            }

            try {
                const result = await makeRequest('/api/details', {
                    file_content: fileContent,
                    filename: selectedFile.name
                });

                if (result.success) {
                    logOutput('✅ 产品详情获取成功');
                } else {
                    logOutput('❌ 获取产品详情失败: ' + result.error);
                }
            } catch (error) {
                logOutput('❌ 获取产品详情错误: ' + error.message);
            }
        }
        
        async function runAllSteps() {
            if (!checkFileBeforeOperation('执行完整工作流程')) {
                return;
            }

            const packageName = document.getElementById('package_name').value.trim();
            const orderId = document.getElementById('order_id').value.trim();

            if (!packageName || !orderId) {
                alert('请输入包名和订单ID');
                return;
            }

            logOutput('🚀 开始执行完整工作流程...');
            logOutput('═'.repeat(50));

            try {
                // Step 1
                logOutput('📋 步骤 1/3: 获取订单信息');
                await getOrderInfo();

                // Step 2
                await new Promise(resolve => setTimeout(resolve, 1000));
                logOutput('🛒 步骤 2/3: 消费产品');
                await consumeProduct();

                // Step 3
                await new Promise(resolve => setTimeout(resolve, 1000));
                logOutput('📊 步骤 3/3: 获取产品详情');
                await productDetails();

                logOutput('═'.repeat(50));
                logOutput('🎉 工作流程完成!');
            } catch (error) {
                logOutput('❌ 工作流程执行错误: ' + error.message);
            }
        }
        
        // 页面初始化
        window.onload = async function() {
            // 加载保存的数据
            const saved = localStorage.getItem('api_tool_data');
            if (saved) {
                const data = JSON.parse(saved);
                document.getElementById('package_name').value = data.package_name || '';
                document.getElementById('order_id').value = data.order_id || '';
            }

            // 初始化按钮状态
            updateButtonStates();

            // 显示欢迎信息
            logOutput('🌟 Google Play Developer API Tool 已启动');
            logOutput('📝 请选择Google API JSON文件，然后输入包名和订单ID');
            logOutput('💡 操作流程:');
            logOutput('   1. 选择Google API JSON文件');
            logOutput('   2. 输入包名和订单ID');
            logOutput('   3. 生成JWT Token（可选）');
            logOutput('   4. 执行API操作');
            logOutput('═'.repeat(50));

            // 绑定输入事件
            document.getElementById('package_name').addEventListener('input', saveData);
            document.getElementById('order_id').addEventListener('input', saveData);
        };
    </script>
</body>
</html>'''
            self.wfile.write(html_content.encode())
            
        elif self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(404)

    def handle_api_request(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            response = {'success': False, 'error': 'Unknown endpoint', 'logs': []}

            if self.path == '/api/get_order':
                response = self.execute_api_call(
                    lambda: self.handle_get_order(data),
                    "获取订单信息"
                )

            elif self.path == '/api/consume':
                response = self.execute_api_call(
                    lambda: self.handle_consume(data),
                    "消费产品"
                )

            elif self.path == '/api/details':
                response = self.execute_api_call(
                    lambda: self.handle_details(data),
                    "获取产品详情"
                )

            elif self.path == '/api/upload_file':
                response = self.handle_file_upload(data)

            elif self.path == '/api/file_info':
                response = self.handle_file_info()

            elif self.path == '/api/generate_jwt':
                response = self.execute_api_call(
                    lambda: self.handle_generate_jwt(data),
                    "生成JWT Token"
                )

            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            error_response = {'success': False, 'error': str(e), 'logs': []}
            self.wfile.write(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))

    def execute_api_call(self, func, operation_name):
        """执行API调用并捕获日志"""
        # 清空之前的日志
        with log_lock:
            captured_logs.clear()

        add_log(f"🚀 开始{operation_name}...")
        start_time = datetime.now()

        try:
            # 重定向输出到日志捕获器
            old_stdout = sys.stdout
            old_stderr = sys.stderr

            sys.stdout = log_capture
            sys.stderr = log_capture

            # 执行函数
            result = func()

            # 恢复输出
            sys.stdout = old_stdout
            sys.stderr = old_stderr

            # 添加成功日志
            elapsed = (datetime.now() - start_time).total_seconds()
            add_log(f"✅ {operation_name}完成 (耗时: {elapsed:.2f}秒)")

            with log_lock:
                return {
                    'success': True,
                    'logs': captured_logs.copy(),
                    'result': result
                }

        except Exception as e:
            # 恢复输出
            sys.stdout = old_stdout
            sys.stderr = old_stderr

            # 添加错误日志
            elapsed = (datetime.now() - start_time).total_seconds()
            add_log(f"❌ {operation_name}失败: {str(e)} (耗时: {elapsed:.2f}秒)")

            with log_lock:
                return {
                    'success': False,
                    'error': str(e),
                    'logs': captured_logs.copy()
                }

    def handle_get_order(self, data):
        """处理获取订单请求"""
        package_name = data.get('package_name', '').strip()
        order_id = data.get('order_id', '').strip()
        file_content = data.get('file_content')
        filename = data.get('filename', 'googleapi.json')

        if not package_name or not order_id:
            raise ValueError("包名和订单ID不能为空")

        if not file_content:
            raise ValueError("请提供Google API JSON文件内容")

        add_log(f"📱 包名: {package_name}")
        add_log(f"🆔 订单ID: {order_id}")

        # 处理文件内容
        success, result_path = file_selector.handle_file_upload(file_content, filename)
        if not success:
            raise ValueError(f"文件处理失败: {result_path}")

        # 设置API使用的文件
        from apis import set_google_api_file
        set_google_api_file(result_path)

        body = {
            "package_name": package_name,
            "order_id": order_id
        }

        get_order(body)
        return {"package_name": package_name, "order_id": order_id}

    def handle_consume(self, data):
        """处理消费产品请求"""
        file_content = data.get('file_content')
        filename = data.get('filename', 'googleapi.json')

        if not file_content:
            raise ValueError("请提供Google API JSON文件内容")

        # 处理文件内容
        success, result_path = file_selector.handle_file_upload(file_content, filename)
        if not success:
            raise ValueError(f"文件处理失败: {result_path}")

        # 设置API使用的文件
        from apis import set_google_api_file
        set_google_api_file(result_path)

        product_consume()
        return {}

    def handle_details(self, data):
        """处理获取产品详情请求"""
        file_content = data.get('file_content')
        filename = data.get('filename', 'googleapi.json')

        if not file_content:
            raise ValueError("请提供Google API JSON文件内容")

        # 处理文件内容
        success, result_path = file_selector.handle_file_upload(file_content, filename)
        if not success:
            raise ValueError(f"文件处理失败: {result_path}")

        # 设置API使用的文件
        from apis import set_google_api_file
        set_google_api_file(result_path)

        product_details()
        return {}

    def handle_file_upload(self, data):
        """处理文件上传"""
        try:
            file_content = data.get('file_content', '')
            filename = data.get('filename', 'googleapi.json')

            if not file_content:
                return {'success': False, 'error': '文件内容为空'}

            success, result = file_selector.handle_file_upload(file_content, filename)

            if success:
                return {
                    'success': True,
                    'message': '文件上传成功',
                    'file_path': result
                }
            else:
                return {'success': False, 'error': result}

        except Exception as e:
            return {'success': False, 'error': f'上传文件时发生错误: {str(e)}'}

    def handle_file_info(self):
        """获取当前文件信息"""
        try:
            file_info = file_selector.get_file_info()
            return {'success': True, 'file_info': file_info}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def handle_generate_jwt(self, data):
        """生成JWT Token"""
        try:
            scope = data.get('scope', 'https://www.googleapis.com/auth/androidpublisher')
            hours = data.get('hours', 1)
            file_content = data.get('file_content')
            filename = data.get('filename', 'googleapi.json')

            if not file_content:
                raise ValueError("请提供Google API JSON文件内容")

            # 处理文件内容
            success, result_path = file_selector.handle_file_upload(file_content, filename)
            if not success:
                raise ValueError(f"文件处理失败: {result_path}")

            # 加载配置并生成token
            jwt_generator.load_google_api_config(result_path)
            result = jwt_generator.generate_jwt_token(scope, hours)

            add_log(f"JWT Token生成成功")
            add_log(f"客户端邮箱: {result['client_email']}")
            add_log(f"过期时间: {result['expiry_time']}")

            return result

        except Exception as e:
            raise Exception(f"生成JWT Token失败: {str(e)}")

def start_server():
    PORT = 8080
    Handler = APIHandler

    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"Server running at http://localhost:{PORT}")
        print("Opening browser...")

        # Open browser after a short delay
        def open_browser():
            import time
            time.sleep(1)
            webbrowser.open(f'http://localhost:{PORT}')

        threading.Thread(target=open_browser, daemon=True).start()

        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    start_server()
