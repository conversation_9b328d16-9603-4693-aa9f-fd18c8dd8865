#!/usr/bin/env python3
import http.server
import socketserver
import json
import threading
import webbrowser
import os
import sys
from datetime import datetime
from apis import get_order, product_consume, product_details

# 全局变量存储日志
captured_logs = []
log_lock = threading.Lock()

def add_log(message):
    """添加日志消息"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    with log_lock:
        captured_logs.append(f"[{timestamp}] {message}")

class LogCapture:
    """捕获标准输出和错误输出"""
    def write(self, text):
        if text.strip():  # 忽略空行
            add_log(text.strip())
    
    def flush(self):
        pass

# 创建日志捕获器
log_capture = LogCapture()

class APIHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            html_content = '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Play Developer API Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="file"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        button { background-color: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background-color: #45a049; }
        button:disabled { background-color: #cccccc; cursor: not-allowed; }
        .run-all { background-color: #2196F3; font-size: 16px; font-weight: bold; }
        .run-all:hover { background-color: #1976D2; }
        .output { background-color: #f8f8f8; border: 1px solid #ddd; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; white-space: pre-wrap; }
        .button-group { text-align: center; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google Play Developer API Tool</h1>
        
        <div class="form-group">
            <label for="package_name">Package Name:</label>
            <input type="text" id="package_name" placeholder="e.g., com.tf.localuv">
        </div>
        
        <div class="form-group">
            <label for="order_id">Order ID:</label>
            <input type="text" id="order_id" placeholder="e.g., GPA.3314-3666-3496-87495">
        </div>
        
        <div class="form-group">
            <label for="api_file">Google API JSON File:</label>
            <input type="file" id="api_file" accept=".json">
        </div>
        
        <div class="button-group">
            <button onclick="getOrderInfo()">1. Get Order Info</button>
            <button id="consume_btn" onclick="consumeProduct()" disabled>2. Consume Product</button>
            <button id="details_btn" onclick="productDetails()" disabled>3. Product Details</button>
        </div>
        
        <div class="button-group">
            <button class="run-all" onclick="runAllSteps()">Run All Steps</button>
        </div>
        
        <div class="form-group">
            <label for="output">Output:</label>
            <div id="output" class="output"></div>
        </div>
        
        <div class="button-group">
            <button onclick="clearOutput()">Clear Output</button>
        </div>
    </div>

    <script>
        // 基础函数
        function logOutput(message) {
            const output = document.getElementById('output');
            output.textContent += message + '\\n';
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('output').textContent = '';
        }
        
        // 数据保存
        function saveData() {
            const data = {
                package_name: document.getElementById('package_name').value,
                order_id: document.getElementById('order_id').value
            };
            localStorage.setItem('api_tool_data', JSON.stringify(data));
        }
        
        // 网络请求
        async function makeRequest(endpoint, data) {
            try {
                logOutput('⏳ 正在处理请求...');
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data || {})
                });
                const result = await response.json();
                
                // 显示服务器返回的日志
                if (result.logs && result.logs.length > 0) {
                    result.logs.forEach(log => logOutput(log));
                }
                
                return result;
            } catch (error) {
                logOutput('❌ 网络错误: ' + error.message);
                return { success: false, error: error.message };
            }
        }
        
        // API调用函数
        async function getOrderInfo() {
            const packageName = document.getElementById('package_name').value.trim();
            const orderId = document.getElementById('order_id').value.trim();
            
            if (!packageName || !orderId) {
                alert('请输入包名和订单ID');
                return;
            }
            
            const result = await makeRequest('/api/get_order', {
                package_name: packageName,
                order_id: orderId
            });
            
            if (result.success) {
                document.getElementById('consume_btn').disabled = false;
                document.getElementById('details_btn').disabled = false;
                logOutput('✅ 订单信息获取成功');
            } else {
                logOutput('❌ 获取订单信息失败: ' + result.error);
            }
        }
        
        async function consumeProduct() {
            const result = await makeRequest('/api/consume', {});
            
            if (result.success) {
                logOutput('✅ 产品消费成功');
            } else {
                logOutput('❌ 产品消费失败: ' + result.error);
            }
        }
        
        async function productDetails() {
            const result = await makeRequest('/api/details', {});
            
            if (result.success) {
                logOutput('✅ 产品详情获取成功');
            } else {
                logOutput('❌ 获取产品详情失败: ' + result.error);
            }
        }
        
        async function runAllSteps() {
            const packageName = document.getElementById('package_name').value.trim();
            const orderId = document.getElementById('order_id').value.trim();
            
            if (!packageName || !orderId) {
                alert('请输入包名和订单ID');
                return;
            }
            
            logOutput('🚀 开始执行完整工作流程...');
            logOutput('═'.repeat(50));
            
            // Step 1
            logOutput('📋 步骤 1/3: 获取订单信息');
            await getOrderInfo();
            
            // Step 2
            await new Promise(resolve => setTimeout(resolve, 1000));
            logOutput('🛒 步骤 2/3: 消费产品');
            await consumeProduct();
            
            // Step 3
            await new Promise(resolve => setTimeout(resolve, 1000));
            logOutput('📊 步骤 3/3: 获取产品详情');
            await productDetails();
            
            logOutput('═'.repeat(50));
            logOutput('🎉 工作流程完成!');
        }
        
        // 页面初始化
        window.onload = function() {
            // 加载保存的数据
            const saved = localStorage.getItem('api_tool_data');
            if (saved) {
                const data = JSON.parse(saved);
                document.getElementById('package_name').value = data.package_name || '';
                document.getElementById('order_id').value = data.order_id || '';
            }
            
            // 显示欢迎信息
            logOutput('🌟 Google Play Developer API Tool 已启动');
            logOutput('📝 请输入包名和订单ID，然后选择要执行的操作');
            logOutput('💡 提示: 确保 googleapi.json 文件在程序目录下');
            logOutput('═'.repeat(50));
            
            // 绑定输入事件
            document.getElementById('package_name').addEventListener('input', saveData);
            document.getElementById('order_id').addEventListener('input', saveData);
        };
    </script>
</body>
</html>'''
            self.wfile.write(html_content.encode())
            
        elif self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            super().do_GET()
    
    def do_POST(self):
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(404)

    def handle_api_request(self):
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            response = {'success': False, 'error': 'Unknown endpoint', 'logs': []}

            if self.path == '/api/get_order':
                response = self.execute_api_call(
                    lambda: self.handle_get_order(data),
                    "获取订单信息"
                )

            elif self.path == '/api/consume':
                response = self.execute_api_call(
                    lambda: self.handle_consume(),
                    "消费产品"
                )

            elif self.path == '/api/details':
                response = self.execute_api_call(
                    lambda: self.handle_details(),
                    "获取产品详情"
                )

            self.send_response(200)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))

        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.end_headers()
            error_response = {'success': False, 'error': str(e), 'logs': []}
            self.wfile.write(json.dumps(error_response, ensure_ascii=False).encode('utf-8'))

    def execute_api_call(self, func, operation_name):
        """执行API调用并捕获日志"""
        # 清空之前的日志
        with log_lock:
            captured_logs.clear()

        add_log(f"🚀 开始{operation_name}...")
        start_time = datetime.now()

        try:
            # 重定向输出到日志捕获器
            old_stdout = sys.stdout
            old_stderr = sys.stderr

            sys.stdout = log_capture
            sys.stderr = log_capture

            # 执行函数
            result = func()

            # 恢复输出
            sys.stdout = old_stdout
            sys.stderr = old_stderr

            # 添加成功日志
            elapsed = (datetime.now() - start_time).total_seconds()
            add_log(f"✅ {operation_name}完成 (耗时: {elapsed:.2f}秒)")

            with log_lock:
                return {
                    'success': True,
                    'logs': captured_logs.copy(),
                    'result': result
                }

        except Exception as e:
            # 恢复输出
            sys.stdout = old_stdout
            sys.stderr = old_stderr

            # 添加错误日志
            elapsed = (datetime.now() - start_time).total_seconds()
            add_log(f"❌ {operation_name}失败: {str(e)} (耗时: {elapsed:.2f}秒)")

            with log_lock:
                return {
                    'success': False,
                    'error': str(e),
                    'logs': captured_logs.copy()
                }

    def handle_get_order(self, data):
        """处理获取订单请求"""
        package_name = data.get('package_name', '').strip()
        order_id = data.get('order_id', '').strip()

        if not package_name or not order_id:
            raise ValueError("包名和订单ID不能为空")

        add_log(f"📱 包名: {package_name}")
        add_log(f"🆔 订单ID: {order_id}")

        # 检查googleapi.json文件
        if not os.path.exists('googleapi.json'):
            add_log("⚠️  未找到 googleapi.json 文件")
            raise FileNotFoundError("请确保 googleapi.json 文件在程序目录下")

        body = {
            "package_name": package_name,
            "order_id": order_id
        }

        get_order(body)
        return {"package_name": package_name, "order_id": order_id}

    def handle_consume(self):
        """处理消费产品请求"""
        product_consume()
        return {}

    def handle_details(self):
        """处理获取产品详情请求"""
        product_details()
        return {}

def start_server():
    PORT = 8080
    Handler = APIHandler

    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print(f"Server running at http://localhost:{PORT}")
        print("Opening browser...")

        # Open browser after a short delay
        def open_browser():
            import time
            time.sleep(1)
            webbrowser.open(f'http://localhost:{PORT}')

        threading.Thread(target=open_browser, daemon=True).start()

        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    start_server()
