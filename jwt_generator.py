import jwt
import datetime
import json

# 读取私钥
with open("googleapi.json", "r") as f:
    api_text = f.read()
    api_json = json.loads(api_text)



# JWT 载荷（payload）
json = {
    "iss": api_json["client_email"],
    "scope": "https://www.googleapis.com/auth/androidpublisher",
    "aud": api_json["token_uri"],
    "exp": datetime.datetime.utcnow() + datetime.timedelta(hours=1),  # 过期时间,
    "iat": datetime.datetime.utcnow(),
}

private_key = api_json["private_key"]

# 生成 token（RS256 非对称加密算法
jwt_token = jwt.encode(json, private_key, algorithm="RS256")
