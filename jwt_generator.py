import jwt
import datetime
import json
import os
import configparser

class JWTGenerator:
    """JWT Token 生成器 - 核心业务逻辑，不包含UI"""

    def __init__(self, config_file="jwt_config.ini"):
        self.config_file = config_file
        self.api_json = None
        self.jwt_token = None
        self.expiry_time = None

    def load_config(self):
        """加载配置文件"""
        config = configparser.ConfigParser()

        if os.path.exists(self.config_file):
            config.read(self.config_file, encoding="utf-8")

        return config

    def save_config(self, config):
        """保存配置文件"""
        with open(self.config_file, "w", encoding="utf-8") as f:
            config.write(f)

    def get_last_file_path(self):
        """获取上次使用的文件路径"""
        config = self.load_config()
        return config.get("DEFAULT", "last_file", fallback=None)

    def get_last_directory(self):
        """获取上次使用的目录"""
        config = self.load_config()
        return config.get("DEFAULT", "last_directory", fallback=os.getcwd())

    def save_file_path(self, file_path):
        """保存文件路径到配置"""
        config = self.load_config()

        if "DEFAULT" not in config:
            config.add_section("DEFAULT")

        config.set("DEFAULT", "last_file", file_path)
        config.set("DEFAULT", "last_directory", os.path.dirname(file_path))
        self.save_config(config)

    def load_google_api_config(self, file_path):
        """加载Google API配置文件"""
        if not file_path:
            raise ValueError("文件路径不能为空")

        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件未找到: {file_path}")

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                api_text = f.read()
                self.api_json = json.loads(api_text)

            # 验证必要的字段
            required_fields = ["client_email", "token_uri", "private_key"]
            for field in required_fields:
                if field not in self.api_json:
                    raise ValueError(f"JSON文件缺少必要字段: {field}")

            # 保存文件路径
            self.save_file_path(file_path)

            return self.api_json

        except json.JSONDecodeError as e:
            raise ValueError(f"JSON文件格式错误: {str(e)}")
        except Exception as e:
            raise Exception(f"读取文件时发生错误: {str(e)}")

    def generate_jwt_token(self, scope="https://www.googleapis.com/auth/androidpublisher", hours=1):
        """生成JWT Token"""
        if not self.api_json:
            raise ValueError("请先加载Google API配置文件")

        # JWT 载荷（payload）
        self.expiry_time = datetime.datetime.utcnow() + datetime.timedelta(hours=hours)

        payload = {
            "iss": self.api_json["client_email"],
            "scope": scope,
            "aud": self.api_json["token_uri"],
            "exp": self.expiry_time,
            "iat": datetime.datetime.utcnow(),
        }

        private_key = self.api_json["private_key"]

        # 生成 token（RS256 非对称加密算法）
        self.jwt_token = jwt.encode(payload, private_key, algorithm="RS256")

        return {
            "token": self.jwt_token,
            "expiry_time": self.expiry_time,
            "client_email": self.api_json["client_email"],
            "scope": scope
        }

    def get_token_info(self):
        """获取当前token信息"""
        if not self.jwt_token:
            return None

        return {
            "token": self.jwt_token,
            "expiry_time": self.expiry_time,
            "client_email": self.api_json.get("client_email") if self.api_json else None
        }

# 向后兼容的函数接口
def generate_jwt_from_file(file_path, scope="https://www.googleapis.com/auth/androidpublisher", hours=1):
    """从文件生成JWT Token - 简单接口"""
    generator = JWTGenerator()
    generator.load_google_api_config(file_path)
    return generator.generate_jwt_token(scope, hours)